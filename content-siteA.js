(function runSiteA() {
  // Wait a tick for the page to render selects etc.
  window.addEventListener("load", () => {
    injectFloatingButton("fb-save-site-a", "Save from Site A", async () => {
      try {
        const data = {};

        // Read text inputs
        for (const [key, sel] of Object.entries(siteAFields)) {
          const el = sel ? qs(sel) : null;
          if (!el) continue;
          // For all selects store both value + visible text
          if (el.tagName === "SELECT") {
            data[key] = getInputValue(el); // { value, text }
          } else {
            data[key] = getInputValue(el); // string
          }
        }

        // Normalize into Site B-friendly values where needed
        const normalized = normalize(data);

        await chrome.storage.local.set({
          bridgeFormData: normalized,
          bridgeSavedAt: Date.now(),
        });
        toast("Saved data from Site A ✅");
        console.log("[Form Bridge] Saved:", normalized);
      } catch (e) {
        console.error(e);
        toast("Failed to save from Site A", false);
      }
    });
  });
})();
