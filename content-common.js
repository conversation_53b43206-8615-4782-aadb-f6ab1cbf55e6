// Mappings (selectors)

// 777 
const siteAFields = {
  sewadarId: "input[placeholder='Sewadar ID']",
  aadhar: "input[placeholder='<PERSON><PERSON>har']",
  satsangGhar: "select.form-select:nth-of-type(1)",
  name: "input[placeholder='Name']",
  fatherHusband: "input[placeholder='Father / Husband']",
  gender: "select.form-select:nth-of-type(2)",
  address: "input[placeholder='Address']",
  pin: "input[placeholder='Pin']",
  dob: "input[placeholder='Date of Birth']",
  maritalStatus: "select.form-select:nth-of-type(3)",
  mobile: "input[placeholder='Mobile No.']",
  emergencyContact: "input[placeholder='Emergency Contact']",
  initiationDate: "input[placeholder='Date of Initiation']",
  initiationPlace: "input[placeholder='Place of Initiation']",
  initiatedBy: "select.form-select:nth-of-type(4)",
  email: "input[placeholder='Email']",
  qualification: "input[placeholder='Qualification']",
  bloodGroup: "select.form-select:nth-of-type(5)",
  specialization: "input[placeholder='Specialization']",
  specializationDetails: "input[placeholder='Specialization Details']",
  introducer1: "input[placeholder='Introducer/Guarantor 1']",
  introducer2: "input[placeholder='Introducer/Guarantor 2']",
  medicalOfficer: "input[placeholder='Medical Officer']",
  securityHod: "input[placeholder='Security HOD']",
  sewaDepartment: "select.form-select:nth-of-type(6)"
};

// zims 
const siteBFields = {
  sewadarId: "#application-no",
  aadhar: "#aadhaar",
  satsangGhar: "#centre", // mapping Satsang Ghar → Centre (confirm on your page)
  name: "#applicantName",
  fatherHusband: "#applicantFatherHusbandName",
  gender: "#applicantGender",
  address: "#residentialAddress",
  pin: "#residentialPinCode",
  // dates (dynamic IDs) → we will detect intelligently in code
  dob: null,               // Date of birth
  initiationDate: null,    // Date of initiation
  initiationPlace: "#placeOfInitiation",
  initiatedBy: "#initiatedBy",
  email: "#emailId",
  qualification: "#qualification",
  bloodGroup: "#bloodGroup",
  specialization: "#specialisation",
  specializationDetails: "#specialisationDetails",
  introducer1: "#introducerSign",
  introducer2: "#guarantorSign",
  medicalOfficer: "#medicalOfficer",
  securityHod: "#securitySewadar",
  sewaDepartment: "#deparments", // (sic) per your list
  mobile: "#mobileNumber",
  emergencyContact: "#emergencyContact",
  // Optional extras if you ever need them:
  area: "#area",
  centre: "#centre",
  residentialStateName: "#residentialStateName",
  residentialCityName: "#residentialCityName"
};

// ---------- Utilities ----------

function qs(sel, root = document) { return root.querySelector(sel); }
function qsa(sel, root = document) { return Array.from(root.querySelectorAll(sel)); }

function getInputValue(el) {
  if (!el) return "";
  if (el.tagName === "SELECT") {
    const value = el.value ?? "";
    // also store visible text for easier mapping later
    const text = el.selectedOptions?.[0]?.text?.trim?.() || "";
    return { value, text };
  }
  return el.value ?? "";
}

function setNativeValue(el, value) {
  if (!el) return false;
  // For inputs/selects – set value + dispatch events so frameworks notice
  const tag = el.tagName;
  if (tag === "INPUT" || tag === "TEXTAREA") {
    el.focus();
    el.value = value ?? "";
    el.dispatchEvent(new Event("input", { bubbles: true }));
    el.dispatchEvent(new Event("change", { bubbles: true }));
    return true;
  }
  if (tag === "SELECT") {
    el.value = value ?? "";
    el.dispatchEvent(new Event("input", { bubbles: true }));
    el.dispatchEvent(new Event("change", { bubbles: true }));
    return true;
  }
  return false;
}

function setSelectByValueOrText(selectEl, desired) {
  if (!selectEl) return false;
  if (typeof desired === "string") {
    // Try value match first
    if ([...selectEl.options].some(o => o.value === desired)) {
      return setNativeValue(selectEl, desired);
    }
    // Try text match (case-insensitive)
    const match = [...selectEl.options].find(o => o.text.trim().toLowerCase() === desired.trim().toLowerCase());
    if (match) return setNativeValue(selectEl, match.value);
    return false;
  }
  if (desired && typeof desired === "object") {
    const { value, text } = desired;
    return setSelectByValueOrText(selectEl, value || text || "");
  }
  return false;
}

// For custom dropdowns (if any). You can refine per library if needed.
async function pickFromCustomDropdownByText(containerSelectorOrEl, textToPick) {
  const root = typeof containerSelectorOrEl === "string" ? qs(containerSelectorOrEl) : containerSelectorOrEl;
  if (!root) return false;
  // Try to click open
  root.click();
  await new Promise(r => setTimeout(r, 150));
  // Look for listbox/menu options near it
  const candidates = qsa("[role='option'], [role='listbox'] * , .dropdown-menu .dropdown-item, .menu, .menu *");
  const target = candidates.find(n => n.textContent?.trim()?.toLowerCase() === textToPick.trim().toLowerCase());
  if (target) {
    target.click();
    return true;
  }
  return false;
}

// Normalize values from Site A → Site B where labels differ
function normalize(data) {
  const out = { ...data };

  // Marital Status differences (A has Unmarried vs B might have Single)
  if (out.maritalStatus) {
    const t = (out.maritalStatus.text || out.maritalStatus.value || out.maritalStatus).toString().toLowerCase();
    if (t.includes("unmarried") || t.includes("single")) out.maritalStatus = "Single";
    else if (t.includes("married")) out.maritalStatus = "Married";
    else if (t.includes("divorce")) out.maritalStatus = "Divorcee";
    else if (t.includes("widow")) out.maritalStatus = "Widow";
  }

  // Gender passthrough ("Male"/"Female") – adjust if your target uses M/F codes
  if (out.gender) {
    const g = (out.gender.text || out.gender.value || out.gender).toString().toLowerCase();
    if (g.startsWith("m")) out.gender = "Male";
    else if (g.startsWith("f")) out.gender = "Female";
  }

  // Blood group likely same
  if (out.bloodGroup && typeof out.bloodGroup === "object") {
    out.bloodGroup = out.bloodGroup.value || out.bloodGroup.text;
  }

  // Satsang Ghar → Centre: Site A had numeric value IDs; keep the "value" if present
  if (out.satsangGhar && typeof out.satsangGhar === "object") {
    out.satsangGhar = out.satsangGhar.value || out.satsangGhar.text;
  }

  // Initiated By – Site A values were 1/2/3; try to pass numeric value if available
  if (out.initiatedBy && typeof out.initiatedBy === "object") {
    out.initiatedBy = out.initiatedBy.value || out.initiatedBy.text;
  }

  return out;
}

// Helper to safely inject a floating button
function injectFloatingButton(id, label, onClick) {
  if (document.getElementById(id)) return; // avoid duplicates
  const btn = document.createElement("button");
  btn.id = id;
  btn.textContent = label;
  Object.assign(btn.style, {
    position: "fixed",
    bottom: "16px",
    right: "16px",
    zIndex: 999999,
    background: "darkred",
    color: "white",
    border: "none",
    borderRadius: "8px",
    padding: "10px 14px",
    fontSize: "14px",
    boxShadow: "0 2px 6px rgba(0,0,0,.3)",
    cursor: "pointer"
  });
  btn.addEventListener("click", onClick);
  document.body.appendChild(btn);
}

function toast(msg, ok = true) {
  const id = "form-bridge-toast";
  let el = document.getElementById(id);
  if (!el) {
    el = document.createElement("div");
    el.id = id;
    Object.assign(el.style, {
      position: "fixed",
      bottom: "64px",
      right: "16px",
      background: "#222",
      color: "white",
      padding: "8px 12px",
      borderRadius: "6px",
      zIndex: 999999
    });
    document.body.appendChild(el);
  }
  el.style.background = ok ? "#2e7d32" : "#b71c1c";
  el.textContent = msg;
  setTimeout(() => el.remove(), 2500);
}
