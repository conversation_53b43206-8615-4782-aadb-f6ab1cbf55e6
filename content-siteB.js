(function runSiteB() {
  window.addEventListener("load", () => {
    injectFloatingButton("fb-fill-site-b", "Fill on Site B", async () => {
      try {
        const { bridgeFormData } = await chrome.storage.local.get(
          "bridgeFormData"
        );
        if (!bridgeFormData) {
          toast("No saved data found. Save on Site A first.", false);
          return;
        }

        const d = bridgeFormData;

        // Helper to set simple fields by selector
        const setField = (sel, val) => {
          if (!sel) return false;
          const el = qs(sel);
          if (!el) return false;
          if (el.tagName === "SELECT") return setSelectByValueOrText(el, val);
          return setNativeValue(el, val);
        };

        // --- Fill simple fields ---
        setField(siteBFields.sewadarId, d.sewadarId);
        setField(siteBFields.aadhar, d.aadhar);
        setField(siteBFields.name, d.name);
        setField(siteBFields.fatherHusband, d.fatherHusband);
        setField(siteBFields.address, d.address);
        setField(siteBFields.pin, d.pin);
        setField(siteBFields.mobile, d.mobile);
        setField(siteBFields.emergencyContact, d.emergencyContact);
        setField(siteBFields.initiationPlace, d.initiationPlace);
        setField(siteBFields.email, d.email);
        setField(siteBFields.specialization, d.specialization);
        setField(siteBFields.specializationDetails, d.specializationDetails);
        setField(siteBFields.medicalOfficer, d.medicalOfficer);
        setField(siteBFields.securityHod, d.securityHod);

        // --- Dropdowns (native <select> preferred) ---
        setField(siteBFields.gender, d.gender);
        setField(siteBFields.maritalStatus, d.maritalStatus);
        setField(siteBFields.bloodGroup, d.bloodGroup);
        setField(siteBFields.initiatedBy, d.initiatedBy);
        setField(siteBFields.qualification, d.qualification);
        setField(siteBFields.sewaDepartment, d.sewaDepartment);

        // Satsang Ghar → Centre
        setField(siteBFields.satsangGhar, d.satsangGhar);

        // --- Dates (dynamic ID fallbacks) ---
        // Try common cases first: real <input type="date"> fields
        function setDateSmart(preferredSelector, value, labelHints = []) {
          if (!value) return false;

          // 1) preferred fixed selector (if you later add one)
          if (preferredSelector && setField(preferredSelector, value))
            return true;

          // 2) Any input[type='date'] candidates
          let candidates = qsa("input[type='date']");
          // 3) Fallback: inputs that look like date pickers (by placeholder or aria-label)
          if (candidates.length === 0) {
            candidates = qsa(
              "input[placeholder*='Date' i], input[aria-label*='Date' i]"
            );
          }
          // 4) If hints given ("Date of birth", etc.), try to match by closest label text
          if (labelHints.length && candidates.length > 1) {
            const scored = candidates
              .map((el) => {
                let score = 0;
                const labelText = findNearestLabelText(el).toLowerCase();
                for (const hint of labelHints) {
                  if (labelText.includes(hint.toLowerCase())) score += 1;
                }
                return { el, score };
              })
              .sort((a, b) => b.score - a.score);
            if (scored[0] && scored[0].score > 0) {
              return setNativeValue(scored[0].el, value);
            }
          }
          // 5) Else set first available candidate that is empty
          const target = candidates.find((el) => !el.value) || candidates[0];
          return setNativeValue(target, value);
        }

        function findNearestLabelText(inputEl) {
          // Try <label for="id">
          if (inputEl.id) {
            const lbl = qs(`label[for='${CSS.escape(inputEl.id)}']`);
            if (lbl) return lbl.textContent || "";
          }
          // Try wrapping label
          const parentLabel = inputEl.closest("label");
          if (parentLabel) return parentLabel.textContent || "";
          // Nearby text within form-group
          const group = inputEl.closest(".form-group, .mb-3, .row, div");
          if (group) {
            const lab = group.querySelector("label");
            if (lab) return lab.textContent || "";
          }
          return "";
        }

        // Dates from Site A are likely in yyyy-mm-dd if native date input used there
        setDateSmart(siteBFields.dob, d.dob, ["birth", "dob"]);
        setDateSmart(siteBFields.initiationDate, d.initiationDate, [
          "initiation",
        ]);

        // --- Optional: Introducer / Guarantor images or names ---
        setField(siteBFields.introducer1, d.introducer1);
        setField(siteBFields.introducer2, d.introducer2);

        toast("Filled Site B ✅");
        console.log("[Form Bridge] Filled with:", d);
      } catch (e) {
        console.error(e);
        toast("Failed to fill Site B", false);
      }
    });
  });
})();
